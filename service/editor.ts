'use server'

import { enhanceUserPrompt } from "@/lib/ai/image";
import { prisma } from "@/lib/prisma";
import { SubscriptionType } from "@/lib/generated/prisma";
import { editorInputSchema, EditorInput } from "@/lib/schemas/editor.schema";

interface APIResponse {
  taskId: string;
  status: "OK" | "FAILED";
  metadata: any;
}

const MODEL_CREDITS: number = 10;

export async function createEditor(userId: string, parameters: EditorInput) {
    const result = editorInputSchema.safeParse(parameters);
    if (!result.success) {
        throw new Error(result.error.message);
    }
    const input = result.data;
    // Extract user ID from session
        const user = await prisma.user.findUnique({
          where: { id: userId }
        });
        
        if (!user) {
         throw new Error("User not found");
        }
        
      const userSubscription = await prisma.subscription.findUnique({
        where: { userId: user.id }
      });
      if (!userSubscription) {
        throw new Error("User subscription not found");
      }
      if( userSubscription.type === SubscriptionType.FREE){
        throw new Error("Free users cannot use this feature");
      }
      //calculate the credit cost
      const numImages = input.num_images as number;
      const creditCost = MODEL_CREDITS * numImages;
      if (userSubscription.creditsRemaining < creditCost) {
        throw new Error("Not enough credits");
      }
      //Enhance the user prompt
      const enhancePrompt = await enhanceUserPrompt(input.prompt as string);
      if (enhancePrompt.error) {
       throw new Error( enhancePrompt.error);
      }
    
      //update the user subscription
      const updatedSubscription = await prisma.subscription.update({
        where: { userId: user.id },
        data: { 
          creditsRemaining: {
            decrement: creditCost
          }
        }
      });
      const seed = input.seed ?? Math.floor(Math.random() * 1000000);
     
      const fullInput = {...input, seed}
       //如果aspect_ratio为空，就删除aspect_ratio属性
       if(input.aspect_ratio === "match_input_image"){
        delete fullInput.aspect_ratio;
      }
      // Create image editor record
        const imageEditor = await prisma.editor.create({
          data: {
            userId: user.id,
            userPrompt: input.prompt as string,
            prompt: enhancePrompt.prompt as string,
            modelName: 'kontextpro',
            modelId: 'cmbf4iwt7000a0djvaokucr04',
            parameters: fullInput,
            imageUrl: input.image_url as string,
            imageNum: input.num_images as number,
            status: "INIT",
            creditsUsed: creditCost
          }
        });
        const webhook = `${process.env.SITE_URL}/api/callback/kontext`;
        const resp = await falFluxKontext({input: fullInput, webhook});
        if (resp.status === "OK") {
          await prisma.editor.update({
            where: { id: imageEditor.id },
            data: { status: "PENDING", taskId: resp.taskId, metadata: resp.metadata }
          });
          return imageEditor.id
        }else{
          await prisma.editor.update({
            where: { id: imageEditor.id },
            data: { status: "FAILED", metadata: resp.metadata }
          });
          //back user credits
          await prisma.subscription.update({
            where: { userId: user.id },
            data: { 
              creditsRemaining: {
                increment: creditCost
              }
            }
          });
        }
        
      // return response and user remaining credits 
      return null;
}

async function falFluxKontext({input, webhook}:{input: any, webhook: string}): Promise<APIResponse> {
      // Add generation ID to the request headers to track it
      const api = `https://queue.fal.run/fal-ai/flux-pro/kontext?fal_webhook=${webhook}`;
       const responseApi = await fetch(api, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Key ${process.env.FAL_KEY}`
          },
          body: JSON.stringify(input)
        });
      const responseData = await responseApi.json();
      if (responseData?.request_id) {
        return {taskId: responseData.request_id, status: "OK", metadata: responseData};
      }
      return {taskId: "", status: "FAILED", metadata: responseData};
}