import { MetadataRoute } from 'next'

export default function sitemap(): MetadataRoute.Sitemap {
  const baseUrl = 'https://imagefox.art';

  // 静态路由
  const staticRoutes = [
    { url: '/', lastModified: new Date() },
    { url: '/pricing', lastModified: new Date() },
    { url: '/about', lastModified: new Date() },
    { url: '/privacy-policy', lastModified: new Date() },
    { url: '/terms-of-service', lastModified: new Date() },
    { url: '/login', lastModified: new Date() },
    { url: '/register', lastModified: new Date() },
  ];

  // 动态路由 - 这里需要您根据实际项目添加动态生成的路由
  // 例如，如果您有博客文章或产品页面，可以从数据库或API获取这些路由
  // const dynamicRoutes = await getDynamicRoutes();

  // 合并所有路由
  const allRoutes = [
    ...staticRoutes,
    // ...dynamicRoutes
  ];

  // 转换为sitemap格式
  return allRoutes.map(route => ({
    url: `${baseUrl}${route.url}`,
    lastModified: route.lastModified,
    // 可选：设置更改频率和优先级
    // changeFrequency: 'daily',
    // priority: 0.8,
  }));
}

// 示例：从API或数据库获取动态路由
// async function getDynamicRoutes() {
//   try {
//     const response = await fetch('YOUR_API_ENDPOINT/posts');
//     const posts = await response.json();
//     
//     return posts.map(post => ({
//       url: `/posts/${post.slug}`,
//       lastModified: new Date(post.updatedAt || post.createdAt || new Date())
//     }));
//   } catch (error) {
//     console.error('Error fetching dynamic routes:', error);
//     return [];
//   }
// }
