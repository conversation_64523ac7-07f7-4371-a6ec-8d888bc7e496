import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import "./globals.css";
import Header from "../components/header";
import Footer from "../components/footer";
import { Toaster } from "../components/ui/sonner";
import { SessionProvider } from "next-auth/react";
import CookieConsent from "../components/cookie-consent";
import { auth } from "@/auth";
import { GoogleAnalytics } from "@next/third-parties/google"

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "ImageFox - Free AI Image Generator & Photo Editor Online",
  description: "Free AI Image GeneraCreate stunning images with ImageFox's free AI image generator and editor. Transform text to images, edit photos with AI, and enhance visuals instantly. Start creating amazing AI art today.",
};

export default function RootLayout({
  children,
  auth,
}: Readonly<{
  children: React.ReactNode;
  auth: React.ReactNode;
}>) {
  
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <SessionProvider> {/* 添加 refetchInterval={5} (每5秒刷新一次) */}
        <div className="w-full bg-white text-black font-['Inter']">
          <Header />
          {children}
          <Footer />
        </div>
        
        {auth}
        </SessionProvider>
        <CookieConsent
        companyName="ImageFox"
        privacyPolicyUrl="/privacy-policy"
      />
        <GoogleAnalytics gaId="G-ZBXXHST9LV" />
        <Toaster position="top-center" />
      </body>
    </html>
  );
}
