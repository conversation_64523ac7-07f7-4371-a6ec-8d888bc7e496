export default function PhotoEditorDetail() {
    return (
        <main className="max-w-7xl mx-auto">
            <div className="flex flex-col gap-2 text-center w-full mx-auto mt-36 mb-12">
                <h1 className="text-4xl font-bold mb-2">Photo Editor by AI</h1>
                <p className="text-gray-600 text-left mb-4">
                    Use AI models to edit photos. This is a photo editing feature, which uses artificial intelligence to edit photos based on the text you input.
                </p>
                <div className="bg-blue-50 border border-blue-100 rounded-lg p-6 mb-6 text-left">
                    <h2 className="text-2xl font-semibold mb-2 text-blue-700">Why use AI for photo editing?</h2>
                    <ul className="list-disc pl-6 text-gray-700 space-y-1">
                        <li>One-click enhancement: Instantly improve photo quality, lighting, and colors.</li>
                        <li>Remove unwanted objects or backgrounds with natural results.</li>
                        <li>Apply creative styles, filters, or effects based on your description.</li>
                        <li>Restore old or damaged photos automatically.</li>
                        <li>Personalize images for social media, business, or fun.</li>
                    </ul>
                </div>
                {/* 使用案例 */}
                <div className="bg-white border border-gray-100 rounded-lg p-6 mb-8 shadow text-left">
                    <h2 className="text-xl font-semibold mb-2 text-blue-700">Usage Example</h2>
                    <div className="flex flex-col md:flex-row gap-4 items-center">
                        <img src="https://img.imagefox.art/20389660-411c-4077-a998-cf48562cc90c.jpg" alt="Example before" className="w-40 h-40 object-cover rounded border" />
                        <img src="https://img.imagefox.art/430b76f7-6111-4067-a0de-355a31aae9e4.jpg" alt="Example before" className="w-40 h-40 object-cover rounded border" />
                        <span className="text-2xl text-gray-400">→</span>
                        <img src="https://img.imagefox.art/6790d015-d9c5-4605-a6ba-73cb4e3c37d4-0-l18PU7GqdGPcZM8Zm2IHb_c95105a1d9fb4737a7a0a5f2cb2a0723.jpg" alt="Example after" className="w-40 h-40 object-cover rounded border" />
                        <div className="flex-1">
                            <p className="text-gray-700 mb-1"><span className="font-semibold">Prompt:</span> "Two women standing and chatting"
                            </p>
                            <p className="text-gray-500 text-sm">AI automatically removed the unwanted person and made the sky more vivid, all in seconds.</p>
                        </div>
                    </div>
                </div>
                {/* FAQ */}
                <div className="bg-gray-50 border border-gray-100 rounded-lg p-6 mb-8 text-left">
                    <h2 className="text-xl font-semibold mb-2 text-blue-700">FAQ</h2>
                    <div className="mb-2">
                        <p className="font-medium">Q: Do I need any photo editing skills to use this tool?</p>
                        <p className="text-gray-600 ml-4">A: No, just describe what you want in plain language, and the AI will handle the rest.</p>
                    </div>
                    <div className="mb-2">
                        <p className="font-medium">Q: Is my photo safe and private?</p>
                        <p className="text-gray-600 ml-4">A: Yes, your photos are processed securely and are not shared with third parties.</p>
                    </div>
                    <div>
                        <p className="font-medium">Q: What types of edits can I make?</p>
                        <p className="text-gray-600 ml-4">A: You can enhance, retouch, remove objects, change backgrounds, apply styles, and more.</p>
                    </div>
                </div>
                {/* 社交评论 */}
                <div className="bg-white border border-gray-100 rounded-lg p-6 mb-8 shadow text-left">
                    <h2 className="text-xl font-semibold mb-4 text-blue-700">User Comments</h2>
                    <div className="space-y-4">
                        <div className="flex items-start gap-3">
                            <img src="/default.svg" alt="User avatar" className="w-10 h-10 rounded-full border" />
                            <div>
                                <p className="font-semibold">Alice</p>
                                <p className="text-gray-600 text-sm">“I removed a tourist from my vacation photo in one click. Amazing results!”</p>
                            </div>
                        </div>
                        <div className="flex items-start gap-3">
                            <img src="/default.svg" alt="User avatar" className="w-10 h-10 rounded-full border" />
                            <div>
                                <p className="font-semibold">Bob</p>
                                <p className="text-gray-600 text-sm">“The AI enhancement made my old family photos look brand new. Highly recommended.”</p>
                            </div>
                        </div>
                        <div className="flex items-start gap-3">
                            <img src="/default.svg" alt="User avatar" className="w-10 h-10 rounded-full border" />
                            <div>
                                <p className="font-semibold">Cathy</p>
                                <p className="text-gray-600 text-sm">“Super easy to use, and the results are better than Photoshop for quick edits.”</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
                    <div className="bg-white rounded shadow p-4 border border-gray-100">
                        <h3 className="font-semibold text-lg mb-1">Text-to-Edit</h3>
                        <p className="text-gray-500 text-sm">Describe your desired change, and AI will edit your photo accordingly.</p>
                    </div>
                    <div className="bg-white rounded shadow p-4 border border-gray-100">
                        <h3 className="font-semibold text-lg mb-1">Smart Object Removal</h3>
                        <p className="text-gray-500 text-sm">Erase people, objects, or backgrounds with a single click.</p>
                    </div>
                    <div className="bg-white rounded shadow p-4 border border-gray-100">
                        <h3 className="font-semibold text-lg mb-1">Automatic Enhancement</h3>
                        <p className="text-gray-500 text-sm">Let AI optimize brightness, contrast, and sharpness for you.</p>
                    </div>
                </div>
                <a href="/photo-editor" className="inline-block mt-2">
                    <button className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-8 rounded-lg shadow transition-all duration-200">
                        Start Editing Now
                    </button>
                </a>
            </div>
        </main>
    )
}