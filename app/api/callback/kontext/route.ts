'use server'
import { NextRequest, NextResponse } from "next/server";
import { PrismaClient } from "@/lib/generated/prisma";
import { uploadImageFromUrlToR2 } from "@/lib/storage";

const prisma = new PrismaClient();

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();

    const debug = await prisma.debug.create({
      data: {
        data: body,
        createdAt: new Date()
      }
    })
    const {
      request_id,
      //gateway_request_id,
      status,
      payload,
      //error,
      payload_error
    } = body;

    if(status !== "OK"){
      console.error("Image generation failed for task_id:", request_id);
      return NextResponse.json({ error: "Image generation failed" });
    }
    // Log the webhook for debugging
    //console.log("Kontext Webhook received:", body);

    // Find the image generation record by request_id
    const editor = await prisma.editor.findFirst({
      where: { taskId: request_id }
    });

    if (!editor) {
      console.error("Image generation not found for task_id:", request_id);
      return NextResponse.json({ error: "Image generation not found" }, { status: 404 });
    }
    //check if the image generation is already completed
    if(editor.status === 'COMPLETED'){
      console.log('Image generation already completed:', request_id);
      return NextResponse.json({ success: true });
    }

    // Update the image generation record with status and error info
    await prisma.editor.update({
      where: { id: editor.id },
      data: {
        status: status === "OK" ? "COMPLETED" : "FAILED",
        completedAt: new Date()
      }
    });

    // If successful and payload.images exists, create image records
    if (status === "OK" && payload && Array.isArray(payload.images)) {
      await Promise.allSettled(
        payload.images.map(async (img: any, index: number) => {
          const key = `${request_id}-${index}`;
          const imagePath = await uploadImageFromUrlToR2(key, img.url);
          if(imagePath){
            await prisma.editorImage.create({
              data: {
                editorId: editor.id,
                imageUrl: imagePath,
                userId: editor.userId,
                tmpUrl: img.url
              }
            });
          }
        })
      );
    }

    return NextResponse.json({ success: true });
  } catch (err) {
    console.error("Error processing FAL webhook:", err);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
