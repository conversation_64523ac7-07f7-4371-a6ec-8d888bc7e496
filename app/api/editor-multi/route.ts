import { auth } from "@/auth";
import { prisma } from "@/lib/prisma";
import { UserEditorInputSchema } from "@/lib/schemas/editor.schema";
import { enhanceUserPrompt } from "@/lib/ai/image";

//这个模型调用所需的积分
const USE_CREDIT = 10;

interface APIResponse {
  taskId: string;
  status: "OK" | "FAILED";
  metadata: any;
}

export async function POST(req: Request): Promise<Response> {
    try {
        const body = await req.json();
        const parsed = UserEditorInputSchema.safeParse(body);
        if (!parsed.success) {
            return new Response(
                JSON.stringify({
                    status: 'failed',
                    error: "Invalid input data",
                    details: parsed.error.errors,
                }),
                {
                    headers: {
                        "Content-Type": "application/json",
                    },
                }
            );
        }

        // Make a shallow copy and ensure aspect_ratio is optional for delete to work safely
        let input = {}
        if( parsed.data.aspect_ratio === 'match_input_image') {
            const {aspect_ratio, ...rest} = parsed.data;
            input = rest;
        }else{
            input = parsed.data;
        }

        // 验证用户是否登录
        const session = await auth();
        if (!session?.user?.id) {
            return new Response(
                JSON.stringify({
                    status: 'failed',
                    error: "Unauthorized",
                    message: "You must be logged in to use this feature.",
                }),
                {
                    headers: {
                        "Content-Type": "application/json",
                    },
                }
            );
        }

        // 获取用户信息和订阅信息
        const user = await prisma.user.findUnique({
            where: { id: session.user.id },
            include: { subscription: true }
        });

        if (!user) {
            return new Response(
                JSON.stringify({
                    status: 'failed',
                    error: "User not found",
                }),
                {
                    headers: {
                        "Content-Type": "application/json",
                    },
                }
            );
        }

        if (!user.subscription) {
            return new Response(
                JSON.stringify({
                    status: 'failed',
                    error: "No subscription found",
                }),
                {
                    headers: {
                        "Content-Type": "application/json",
                    },
                }
            );
        }

        if(user.subscription.type === "FREE"){
            return new Response(
                JSON.stringify({
                    status: 'failed',
                    error: "Free users are not allowed to use this feature.",
                }),
                {
                    headers: {
                        "Content-Type": "application/json",
                    },
                }
            );
        }

        // 检查用户积分是否足够
        if (user.subscription.creditsRemaining < USE_CREDIT) {
            return new Response(
                JSON.stringify({
                    status: 'failed',
                    error: "Insufficient credits",
                    message: `You need ${USE_CREDIT} credits but only have ${user.subscription.creditsRemaining} credits remaining.`,
                }),
                {
                    headers: {
                        "Content-Type": "application/json",
                    },
                }
            );
        }

        // 增强用户提示词
        const enhancePrompt = await enhanceUserPrompt(parsed.data.prompt);
        if (enhancePrompt.error) {
            return new Response(
                JSON.stringify({
                    status: 'failed',
                    error: enhancePrompt.error,
                }),
                {
                    headers: {
                        "Content-Type": "application/json",
                    },
                }
            );
        }

        // 扣除用户积分
        await prisma.subscription.update({
            where: { userId: user.id },
            data: {
                creditsRemaining: {
                    decrement: USE_CREDIT
                }
            }
        });

        // 创建EditorMulti数据库记录
        const editorMulti = await prisma.editorMulti.create({
            data: {
                userId: user.id,
                userPrompt: parsed.data.prompt,
                prompt: enhancePrompt.prompt as string,
                parameters: input,
                imageUrls: parsed.data.image_urls,
                status: "INIT",
                creditsUsed: USE_CREDIT
            }
        });

        // 调用AI API
        const webhook = `${process.env.NEXTAUTH_URL}/api/callback/kontext-multi`;
        const resp = await falFluxKontextMulti({input, webhook});

        if (resp.status === "OK") {
            // 更新任务状态为PENDING
            await prisma.editorMulti.update({
                where: { id: editorMulti.id },
                data: {
                    status: "PENDING",
                    taskId: resp.taskId,
                    metadata: resp.metadata
                }
            });

            return new Response(
                JSON.stringify({
                    status: 'ok',
                    taskId: editorMulti.id,
                    message: "Multi-image editing task started successfully",
                }),
                {
                    headers: {
                        "Content-Type": "application/json",
                    },
                }
            );
        } else {
            // API调用失败，更新状态为FAILED
            await prisma.editorMulti.update({
                where: { id: editorMulti.id },
                data: {
                    status: "FAILED",
                    metadata: resp.metadata
                }
            });

            // 返还用户积分
            await prisma.subscription.update({
                where: { userId: user.id },
                data: {
                    creditsRemaining: {
                        increment: USE_CREDIT
                    }
                }
            });

            return new Response(
                JSON.stringify({
                    status: 'failed',
                    error: "AI API call failed",
                    message: "Failed to start multi-image editing task. Your credits have been refunded.",
                }),
                {
                    headers: {
                        "Content-Type": "application/json",
                    },
                }
            );
        }

    } catch (error) {
        console.error("Error in editor-multi API:", error);
        return new Response(
            JSON.stringify({
                status: 'failed',
                error: "Internal server error",
                message: error instanceof Error ? error.message : "Unknown error occurred",
            }),
            {
                headers: {
                    "Content-Type": "application/json",
                },
            }
        );
    }
}

async function falFluxKontextMulti({input, webhook}: {input: any, webhook: string}): Promise<APIResponse> {
    try {
        // 使用多图编辑的API端点
        const api = `https://queue.fal.run/fal-ai/flux-pro/kontext/max/multi?fal_webhook=${webhook}`;
        const responseApi = await fetch(api, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Key ${process.env.FAL_KEY}`
            },
            body: JSON.stringify(input)
        });

        const responseData = await responseApi.json();

        if (responseData?.request_id) {
            return {
                taskId: responseData.request_id,
                status: "OK",
                metadata: responseData
            };
        }

        return {
            taskId: "",
            status: "FAILED",
            metadata: responseData
        };
    } catch (error) {
        console.error("Error calling FAL API:", error);
        return {
            taskId: "",
            status: "FAILED",
            metadata: { error: error instanceof Error ? error.message : "Unknown error" }
        };
    }
}