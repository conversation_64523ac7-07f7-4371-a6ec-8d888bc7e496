'use server'
import { NextRequest } from "next/server";
import { auth } from "@/auth";
import { createEditor } from "@/service/editor";
import { editorInputSchema } from "@/lib/schemas/editor.schema";

export async function POST(req: NextRequest) {
  try {
    // Clone the request to read the body without consuming it
    const parameters = await req.json();
    const params = editorInputSchema.safeParse(parameters);
    if(!params.success){
      return new Response(JSON.stringify({status: 'failed', error: params.error.message }), {
        headers: { "Content-Type": "application/json" }
      });
    }
    // Get the current user session
    const session = await auth()
    if (!session?.user) {
      return new Response(JSON.stringify({status: 'failed', error: "Unauthorized" }), {
        headers: { "Content-Type": "application/json" }
      });
    }
 
    const taskId = await createEditor(session.user.id, params.data);
    //   return response and user remaining credits 
    if(!taskId){
      return new Response(JSON.stringify({status: 'failed', error: "create editor failed" }), {
        headers: { "Content-Type": "application/json" }
      });
    }
    return new Response(JSON.stringify({ status: 'ok', taskId: taskId }));
  
  } catch (error) {
    const message = error instanceof Error ? error.message : "Unknown error";
    return new Response(JSON.stringify({status: 'failed', error: message }), {
      headers: { "Content-Type": "application/json" }
    });
  }
}
