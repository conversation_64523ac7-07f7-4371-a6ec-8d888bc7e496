import { NextRequest, NextResponse } from "next/server";
import { prisma } from "@/lib/prisma";

const R2_DOMAIN_NAME = process.env.R2_DOMAIN_NAME;

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { taskId } = body;
    
    if (!taskId) {
      return new NextResponse(JSON.stringify({ error: "Missing parameters" }), {
        headers: { "Content-Type": "application/json" }
      });
    }
    //test 
    // if(taskId){
    //     return new NextResponse(JSON.stringify({status: "ok", images: ['https://pic.rapidmock.dev/157aaeb7-fa1b-4158-81bf-ab50be263c54-0-Wlf-FrDYQoxxXdG6flkrE.png']}));
    // }
    
    //find imagegeneration by taskId
    const editor = await prisma.editor.findUnique({
      select: {
        id: true,
        status: true
      },
      where: { id: taskId }
    });
    if (!editor) {
      return new NextResponse(JSON.stringify({ error: "Task not found" }), {
        headers: { "Content-Type": "application/json" }
      });
    }
    if(editor.status === "COMPLETED"){
      const images = await prisma.editorImage.findMany({
        where: {
          editorId: editor.id
        }
      });
      if(images.length === 0){
        return new NextResponse(JSON.stringify({status: "completed", images: []}));
      }
      const imagePaths = images.map((image) => `${R2_DOMAIN_NAME}/${image.imageUrl}`);
      return new NextResponse(JSON.stringify({status: "ok", images: imagePaths}));
    }
    return new NextResponse(JSON.stringify({status: 'pending'}));
  } catch (error) {
    return new NextResponse(JSON.stringify({ error: "Internal Server Error" }), {
      headers: { "Content-Type": "application/json" }
    });
  }
}
