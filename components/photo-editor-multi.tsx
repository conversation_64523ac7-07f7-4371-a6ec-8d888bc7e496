'use client'

import { Delete, FileUp, FileUpIcon, FolderOpen, ImageIcon, Loader2, Sparkles, Upload, Wand2, X } from "lucide-react"
import { teardownTraceSubscriber } from "next/dist/build/swc/generated-native"
import { useEffect, useState } from "react"
import { toast } from "sonner"
import { Input } from "./ui/input"
import { Select, SelectContent, SelectGroup, SelectItem, SelectTrigger, SelectValue } from "./ui/select"
import { UserEditorInputSchema, editorTaskRequest } from "@/lib/schemas/editor.schema";
import Image from "next/image"
import { Button } from "./ui/button"
import Uploader from "./uploader"


export default function PhotoEditor() {
    const [prompt, setPrompt] = useState<string>('');
    const [image, setImage ] = useState<string>('');
    const [userImage, setUserImage] = useState<string[]>([]);
    const [loading, setLoading] = useState(false);
    const [uploading, setUploading] = useState(false);
    const [aspectRatio, setAspectRatio] = useState<string>('match_input_image');
    let timeoutId: NodeJS.Timeout | null = null;

    function addImage(urls: string[]) {
        if (urls.length === 0) {
            toast.error("No images uploaded");
            return;
        }
        setUserImage((prev) => [...prev, ...urls]);
        toast.success("Images added successfully");
    }

    async function generateImage() {
        setLoading(true);
        try {
            const result = UserEditorInputSchema.safeParse({
                prompt: prompt,
                image_urls: userImage,
                aspect_ratio: aspectRatio,
            })
            if (!result.success) {
                const messages = result.error.errors.map((error) => error.message);
                console.log(result.error);
                throw new Error(messages.join('; '));
            }
            console.log(result.data)
            const postData = result.data;
            const response = await fetch('/api/editor-multi', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(postData)
            })
            const responseResult = await response.json();
            if(responseResult.status !== 'ok'){
                throw new Error(responseResult.error);
            }
            if(timeoutId){
                clearInterval(timeoutId);
            }
            let timeout = 30
            timeoutId = setInterval(async () => {
                const images = await queryTask(responseResult.taskId);
                if(images){
                   setImage(images[0]);
                    setLoading(false);
                    if(timeoutId){
                        clearInterval(timeoutId);
                    }
                    toast("Success", {
                        description: "Image Loading...",
                    })
                }else{
                    timeout--;
                    if(timeout <= 0){
                        if(timeoutId){
                            console.log('clear timeout')
                            clearInterval(timeoutId);
                        }
                        setLoading(false);
                        toast.error("Timeout");
                    }
                    console.log('timeout ', timeout)
                }
            }, 5000);
        } catch (error) {
            if(error instanceof Error){
                toast.error(error.message);
            } else {
                toast.error("Something went wrong");
            }
            setLoading(false);
            console.error("Error generating image:", error);
        }
    }

    async function queryTask(taskId: string): Promise<string[] | null> {
        const response = await fetch('/api/editor-multi-task', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ taskId })
        });
        const responseResult = await response.json();
        if(responseResult.status !== 'ok'){
            toast.error(responseResult.error);
            return null
        }
        return responseResult.images.length == 0 ? null : responseResult.images;
    }

    return (
        <div className="mb-64">
            <div className="flex flex-row flex-wrap justify-center p-2">
                <div className="relative border border-dashed p-2 border-gray-500 w-full h-[320px] md:w-[600px] md:h-[600px] flex items-center justify-center mx-auto rounded-lg overflow-hidden">
                    {loading ? (
                        <div className="absolute inset-0 flex flex-col items-center justify-center">
                            <Loader2 className="h-8 w-8  animate-spin mb-2" />
                            <span className="text-gray-400">photo generating... please wait</span>
                        </div>
                    ) : image ? (
                        <Image src={image} alt="Generated Image" fill={true} objectFit="contain" />
                    ) : (
                        <div className="flex flex-col items-center justify-center w-full h-full">
                            
                            <span className="text-gray-600 text-lg">please upload an image and enter a prompt</span>
                        </div>
                    )}
                </div>
                <div className="flex flex-col p-2 w-full md:w-[600px]">
                    <div className="flex flex-col">
                        <div>
                            <p className="text-lg font-semibold text-gray-800 text-left">Step 1: Upload Your Images</p>
                            <p className="text-sm text-gray-600 mb-2 text-left">
                                Upload one or more images (up to 3) that you want to edit. These will be the base for the AI modifications.
                            </p>
                            <div className="flex flex-row flex-wrap gap-2 mt-2">
                                {userImage.map((img, index) => (
                                    <div key={index} className="relative w-[100px] h-[100px] border border-gray-300 rounded-md overflow-hidden">
                                        <Image src={img} alt={`Uploaded image ${index + 1}`} fill={true} objectFit="cover" />
                                        <span onClick={() => {
                                            setUserImage(userImage.filter((_, i) => i !== index));
                                        }} className="absolute cursor-pointer top-1 right-1 bg-gray-500 p-0 text-white rounded-full w-4 h-4 flex items-center justify-center">
                                            <X />
                                        </span>
                                    </div>
                                ))}
                                <Uploader onUpload={addImage} />
                            </div>
                        </div>
                    </div>
                    <div className="flex flex-col mt-4">
                        <p className="text-gray-600 text-left">Aspect ratio</p>
                        <div className="flex flex-row items-center justify-between gap-2">
                        <Select name="aspect_ratio" value={aspectRatio} onValueChange={setAspectRatio}>
                            <SelectTrigger className="w-full rounded-sm border border-gray-300  bg-white p-2 focus:outline-none focus:ring disabled:cursor-not-allowed disabled:opacity-50 dark:bg-r8-gray-1">
                                <SelectValue placeholder="选择比例" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectGroup>
                                <SelectItem value="match_input_image">match_input_image</SelectItem>
                                <SelectItem value="21:9">21:9</SelectItem>
                                <SelectItem value="16:9">16:9</SelectItem>
                                <SelectItem value="9:16">9:16</SelectItem>
                                <SelectItem value="5:4">5:4</SelectItem>
                                <SelectItem value="4:3">4:3</SelectItem>
                                <SelectItem value="3:4">3:4</SelectItem>
                                <SelectItem value="1:1">1:1</SelectItem>
                                <SelectItem value="1:2">1:2</SelectItem>
                                <SelectItem value="2:1">2:1</SelectItem>
                                <SelectItem value="3:2">3:2</SelectItem>
                                <SelectItem value="2:3">2:3</SelectItem>
                                <SelectItem value="4:5">4:5</SelectItem>
                                <SelectItem value="9:21">9:21</SelectItem>
                                
                                
                            </SelectGroup>
                            </SelectContent>
                            </Select>
                        </div>
                    </div>
                    <div className="flex flex-col border border-gray-300 rounded-md p-4 mt-4">
                        <div className="flex flex-row items-center justify-start mb-2 gap-2">
                            <Wand2 className="w-5 h-5" />
                            <p className="text-lg font-semibold text-gray-800 text-left"> Creative Prompt</p>
                        </div>
                        <div className="text-sm text-gray-600 mb-3">
                            Describe the image editing effect you want, for example: change the background to a cyberpunk style, or remove a certain person from the picture.
                        </div>

                        <div className="flex flex-row items-center justify-between">
                            <textarea value={prompt} onChange={(e) => setPrompt(e.target.value)} className="w-full h-[100px] border border-gray-300 rounded-md p-2"></textarea>                          
                        </div>
                        <div className="flex flex-row justify-end">
                        <button disabled={loading || uploading} onClick={generateImage} className="cursor-pointer w-full bg-gradient-to-r from-blue-500 to-purple-500 text-white px-4 py-2 rounded-md mt-2">
                                {loading ? ('Generating...') : (
                                    <div className="flex items-center justify-center gap-1">
                                        <Sparkles className="w-4 h-4 mr-2" />
                                        <span>Generate</span>
                                    </div>
                                    )}
                            </button>
                        </div>
                    </div>
                </div>  
            </div>
        </div>
    )
}
  