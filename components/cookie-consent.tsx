"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Cookie, Settings, Shield, BarChart3, Target, X, ChevronUp, Info, Check } from "lucide-react"

interface CookieSettings {
  necessary: boolean
  analytics: boolean
  marketing: boolean
  preferences: boolean
}

interface CookieConsentProps {
  onAccept?: (settings: CookieSettings) => void
  onReject?: () => void
  companyName?: string
  privacyPolicyUrl?: string
}

const handleAccept = (settings: any) => {
    console.log("Cookie设置已保存:", settings)
    // 这里可以集成您的分析工具
  }

  const handleReject = () => {
    console.log("用户拒绝了非必要cookie")
  }


export default function CookieConsent({
  onAccept,
  onReject,
  companyName = "我们的网站",
  privacyPolicyUrl = "/privacy-policy",
}: CookieConsentProps) {
  const [isVisible, setIsVisible] = useState(false)
  const [showDetails, setShowDetails] = useState(false)
  const [isAccepted, setIsAccepted] = useState(false)
  const [settings, setSettings] = useState<CookieSettings>({
    necessary: true, // 必要cookie始终启用
    analytics: false,
    marketing: false,
    preferences: false,
  })

  // 检查是否已经设置过cookie偏好
  useEffect(() => {
    const cookieConsent = localStorage.getItem("cookie-consent")
    if (!cookieConsent) {
      // 延迟显示，提升用户体验
      const timer = setTimeout(() => setIsVisible(true), 1000)
      return () => clearTimeout(timer)
    } else {
      setIsAccepted(true)
    }
  }, [])

  const handleAcceptAll = () => {
    const allAccepted = {
      necessary: true,
      analytics: true,
      marketing: true,
      preferences: true,
    }
    setSettings(allAccepted)
    saveCookiePreferences(allAccepted)
    setIsVisible(false)
    setIsAccepted(true)
    onAccept?.(allAccepted)
  }

  const handleRejectAll = () => {
    const onlyNecessary = {
      necessary: true,
      analytics: false,
      marketing: false,
      preferences: false,
    }
    setSettings(onlyNecessary)
    saveCookiePreferences(onlyNecessary)
    setIsVisible(false)
    setIsAccepted(true)
    onReject?.()
  }

  const handleSavePreferences = () => {
    saveCookiePreferences(settings)
    setIsVisible(false)
    setIsAccepted(true)
    onAccept?.(settings)
  }

  const saveCookiePreferences = (preferences: CookieSettings) => {
    localStorage.setItem(
      "cookie-consent",
      JSON.stringify({
        settings: preferences,
        timestamp: new Date().toISOString(),
      }),
    )
  }

  const handleSettingChange = (key: keyof CookieSettings, value: boolean) => {
    if (key === "necessary") return // 必要cookie不能关闭
    setSettings((prev) => ({ ...prev, [key]: value }))
  }

  const reopenSettings = () => {
    setIsVisible(true)
    setShowDetails(true)
  }

  const handleClose = () => {
    setShowDetails(false)
    setIsVisible(false)
  }

  const cookieTypes = [
    {
      key: "necessary" as keyof CookieSettings,
      title: "必要Cookie",
      description: "这些cookie对网站正常运行是必需的，无法禁用。它们通常仅在您进行操作时设置。",
      icon: Shield,
      required: true,
      examples: "登录状态、购物车、安全设置",
    },
    {
      key: "analytics" as keyof CookieSettings,
      title: "分析Cookie",
      description: "这些cookie帮助我们了解访客如何与网站互动，收集和报告匿名信息。",
      icon: BarChart3,
      required: false,
      examples: "Google Analytics、页面访问统计",
    },
    {
      key: "marketing" as keyof CookieSettings,
      title: "营销Cookie",
      description: "这些cookie用于跟踪访客在网站上的活动，目的是显示相关和个性化的广告。",
      icon: Target,
      required: false,
      examples: "Facebook Pixel、Google Ads、重定向广告",
    },
    {
      key: "preferences" as keyof CookieSettings,
      title: "偏好Cookie",
      description: "这些cookie使网站能够记住您的选择，为您提供更个性化的体验。",
      icon: Settings,
      required: false,
      examples: "语言设置、主题偏好、地区选择",
    },
  ]

  if (!isVisible && !isAccepted) return null

  return (
    <>
      {/* Cookie同意弹窗 */}
      {isVisible && (
        <div className="fixed bottom-0 left-0 right-0 z-50 p-4 bg-white border-t shadow-lg animate-in slide-in-from-bottom-4 duration-300">
          {/* 背景遮罩 */}

          {/* 弹窗内容 */}
          <Card className="w-full max-w-none overflow-visible">
            <CardHeader className="pb-4">
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <Cookie className="w-6 h-6 text-blue-600" />
                  </div>
                  <div>
                    <CardTitle className="text-xl">Cookie设置</CardTitle>
                    <p className="text-sm text-gray-600 mt-1">管理您的隐私偏好</p>
                  </div>
                </div>
                <Button variant="ghost" size="sm" onClick={handleClose} className="h-8 w-8 p-0">
                  <X className="w-4 h-4" />
                </Button>
              </div>
            </CardHeader>

            <CardContent className="space-y-4 overflow-visible">
              {/* 主要说明 */}
              <div className="space-y-3">
                <p className="text-gray-700 leading-relaxed">
                  {companyName}使用cookie来改善您的浏览体验、分析网站流量并个性化内容。
                  您可以选择接受所有cookie，或自定义您的偏好设置。
                </p>

                <div className="flex items-center gap-2 text-sm text-blue-600">
                  <Info className="w-4 h-4" />
                  <a href={privacyPolicyUrl} className="hover:underline">
                    查看我们的隐私政策
                  </a>
                </div>
              </div>

              {/* 快速操作按钮 */}
              {!showDetails && (
                <div className="flex flex-col sm:flex-row gap-3">
                  <Button onClick={handleAcceptAll} className="flex-1 bg-blue-600 hover:bg-blue-700">
                    <Check className="w-4 h-4 mr-2" />
                    接受全部
                  </Button>
                  <Button variant="outline" onClick={handleRejectAll} className="flex-1">
                    仅必要Cookie
                  </Button>
                  <Button variant="ghost" onClick={() => setShowDetails(true)} className="flex-1">
                    <Settings className="w-4 h-4 mr-2" />
                    自定义设置
                  </Button>
                </div>
              )}

              {/* 详细设置 */}
              {showDetails && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <h3 className="text-lg font-semibold">Cookie类型</h3>
                    <Button variant="ghost" size="sm" onClick={() => setShowDetails(false)}>
                      <ChevronUp className="w-4 h-4 mr-1" />
                      收起
                    </Button>
                  </div>

                  <div className="space-y-2">
                    {cookieTypes.map((type, index) => (
                      <div key={type.key}>
                        <div className="flex items-start justify-between p-2 border rounded-lg">
                          <div className="flex items-start gap-2 flex-1">
                            <div className="p-1 bg-gray-100 rounded-lg mt-1">
                              <type.icon className="w-4 h-4 text-gray-600" />
                            </div>
                            <div className="flex-1 space-y-1">
                              <div className="flex items-center gap-2">
                                <h4 className="font-medium text-sm">{type.title}</h4>
                                {type.required && (
                                  <Badge variant="secondary" className="text-xs">
                                    必需
                                  </Badge>
                                )}
                              </div>
                              <p className="text-xs text-gray-600 leading-relaxed">{type.description}</p>
                              <p className="text-xs text-gray-500">
                                <span className="font-medium">示例：</span>
                                {type.examples}
                              </p>
                            </div>
                          </div>
                          <Switch
                            checked={settings[type.key]}
                            onCheckedChange={(checked) => handleSettingChange(type.key, checked)}
                            disabled={type.required}
                            className="ml-2"
                          />
                        </div>
                        {index < cookieTypes.length - 1 && <Separator />}
                      </div>
                    ))}
                  </div>

                  {/* 详细设置的操作按钮 */}
                  <div className="flex flex-col sm:flex-row gap-2 pt-2 border-t">
                    <Button onClick={handleSavePreferences} className="flex-1 bg-blue-600 hover:bg-blue-700 text-sm">
                      保存偏好设置
                    </Button>
                    <Button variant="outline" onClick={handleAcceptAll} className="flex-1 text-sm">
                      接受全部
                    </Button>
                    <Button variant="ghost" onClick={handleRejectAll} className="flex-1 text-sm">
                      仅必要Cookie
                    </Button>
                  </div>
                </div>
              )}

              {/* 底部信息 */}
              <div className="text-xs text-gray-500 bg-gray-50 p-3 rounded-lg">
                <p>您可以随时在网站设置中更改这些偏好。某些功能可能需要特定的cookie才能正常工作。</p>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* 重新打开设置的浮动按钮 */}
      {isAccepted && !isVisible && (
        <Button onClick={reopenSettings} className="fixed bottom-4 left-4 z-40 shadow-lg" size="sm" variant="outline">
          <Cookie className="w-4 h-4 mr-2" />
          Cookie设置
        </Button>
      )}
    </>
  )
}
