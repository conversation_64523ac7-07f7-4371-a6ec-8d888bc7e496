import { z } from "zod";

export const editorInputSchema = z.object({
    prompt: z.string().min(1, "Prompt is required"),
    image_url: z.string().url("A valid image URL is required"),
    guidance_scale: z.number()
        .min(1, "Guidance scale must be between 1 and 20")
        .max(20, "Guidance scale must be between 1 and 20"),
    num_images: z.number()
        .min(1, "Number of images must be between 1 and 4")
        .max(4, "Number of images must be between 1 and 4"),
    output_format: z.enum(['png', 'jpeg']),
    // Ensuring 'match_input_image' is available as it's used in the client UI
    aspect_ratio: z.enum(['1:1', '3:4', '4:3', '16:9', '9:16', '21:9', '9:21', 'match_input_image']).optional(),
    // Seed is required by the backend logic, client should provide it.
    seed: z.number().int("Seed must be an integer").optional(),
    safety_tolerance: z.number().min(1, "Safety tolerance must be between 1 and 5").max(5, "Safety tolerance must be between 1 and 5").default(2),
});

export type EditorInput = z.infer<typeof editorInputSchema>;

//API

export const editorRequest = '/api/model/editor';
export const editorTaskRequest = '/api/model/editor-task';

export const editorMultiInputSchema = z.object({
    prompt: z.string().min(1, "Prompt is required"),
    seed: z.number().int("Seed must be an integer").optional(),
    guidance_scale: z.number()
        .min(1, "Guidance scale must be between 1 and 20")
        .max(20, "Guidance scale must be between 1 and 20")
        .default(3.5),
    sync_mode: z.boolean().default(false),
    num_images: z.number()
        .min(1, "Number of images must be between 1 and 4")
        .max(4, "Number of images must be between 1 and 4")
        .default(1),
    safety_tolerance: z.number()
                      .min(1, "Safety tolerance must be between 1 and 5")
                      .max(5, "Safety tolerance must be between 1 and 5")
                      .default(2),
    output_format: z.enum(['png', 'jpeg']).default('png'),
    aspect_ratio: z.enum(['1:1','2:3', '3:2', '3:4', '4:3', '16:9', '9:16', '21:9', '9:21', 'match_input_image'])
                   .default('match_input_image'),
    image_urls: z.array(z.string().url("A valid image URL is required"))
                .min(1, "At least one image URL is required"),
});
export type EditorMultiInput = z.infer<typeof editorMultiInputSchema>;

export const UserEditorInputSchema = z.object({
    prompt: z.string().min(1, "Prompt is required"),
    aspect_ratio: z.enum(['1:1','2:3', '3:2', '3:4', '4:3', '16:9', '9:16', '21:9', '9:21', 'match_input_image'])
                   .default('match_input_image'),
    image_urls: z.array(z.string().url("A valid image URL is required"))
                .min(1, "At least one image URL is required").max(3, "A maximum of 3 images can be processed at once"),
});

export type UserEditorInput = z.infer<typeof UserEditorInputSchema>;

